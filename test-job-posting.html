<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Job Posting</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        textarea { height: 100px; }
    </style>
</head>
<body>
    <h1>Job Posting Test</h1>
    
    <div class="test-section">
        <h2>Step 1: Test Backend Connection</h2>
        <button onclick="testBackend()">Test Backend Health</button>
        <div id="backendResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Step 2: View Current Jobs</h2>
        <button onclick="getAllJobs()">Get All Jobs</button>
        <div id="allJobsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Step 3: Post a Test Job</h2>
        <form id="jobForm">
            <input type="text" id="title" placeholder="Job Title" value="Test Frontend Developer" required>
            <input type="text" id="company" placeholder="Company" value="Test Company Inc" required>
            <input type="text" id="location" placeholder="Location" value="Remote" required>
            <input type="text" id="experience" placeholder="Experience" value="2+ years">
            <input type="text" id="skills" placeholder="Skills (comma-separated)" value="React, JavaScript, CSS">
            <input type="text" id="salaryRange" placeholder="Salary Range" value="$60,000 - $80,000">
            <select id="jobType">
                <option value="full-time">Full-time</option>
                <option value="part-time">Part-time</option>
                <option value="contract">Contract</option>
            </select>
            <textarea id="description" placeholder="Job Description">We are looking for a talented Frontend Developer to join our team...</textarea>
            <button type="submit">Post Job</button>
        </form>
        <div id="postJobResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Step 4: View Recruiter Jobs</h2>
        <button onclick="getRecruiterJobs()">Get My Jobs</button>
        <div id="recruiterJobsResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3004/api';

        async function testBackend() {
            const result = document.getElementById('backendResult');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                result.className = 'result success';
                result.textContent = `✅ Backend is running: ${data.message}`;
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Backend connection failed: ${error.message}`;
            }
        }

        async function getAllJobs() {
            const result = document.getElementById('allJobsResult');
            try {
                const response = await fetch(`${API_BASE}/jobs`);
                const jobs = await response.json();
                result.className = 'result success';
                result.innerHTML = `✅ Found ${jobs.length} jobs:<br><pre>${JSON.stringify(jobs, null, 2)}</pre>`;
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Failed to get jobs: ${error.message}`;
            }
        }

        async function getRecruiterJobs() {
            const result = document.getElementById('recruiterJobsResult');
            try {
                const response = await fetch(`${API_BASE}/jobs/recruiter/my-jobs`);
                const jobs = await response.json();
                result.className = 'result success';
                result.innerHTML = `✅ Found ${jobs.length} recruiter jobs:<br><pre>${JSON.stringify(jobs, null, 2)}</pre>`;
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Failed to get recruiter jobs: ${error.message}`;
            }
        }

        document.getElementById('jobForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const result = document.getElementById('postJobResult');
            
            const jobData = {
                title: document.getElementById('title').value,
                company: document.getElementById('company').value,
                location: document.getElementById('location').value,
                experience: document.getElementById('experience').value,
                skills: document.getElementById('skills').value,
                salaryRange: document.getElementById('salaryRange').value,
                jobType: document.getElementById('jobType').value,
                description: document.getElementById('description').value
            };

            try {
                const response = await fetch(`${API_BASE}/jobs`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(jobData)
                });

                const data = await response.json();
                
                if (response.ok) {
                    result.className = 'result success';
                    result.innerHTML = `✅ Job posted successfully!<br>Job ID: ${data.jobId}<br><pre>${JSON.stringify(data.job, null, 2)}</pre>`;
                    // Auto-refresh jobs
                    setTimeout(() => {
                        getAllJobs();
                        getRecruiterJobs();
                    }, 1000);
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ Failed to post job: ${data.error}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Network error: ${error.message}`;
            }
        });

        // Auto-test on page load
        window.onload = () => {
            testBackend();
            getAllJobs();
            getRecruiterJobs();
        };
    </script>
</body>
</html>
